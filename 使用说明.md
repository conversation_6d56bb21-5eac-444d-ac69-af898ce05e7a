# FSA账户测试器 - 使用说明

## 功能更新

脚本已更新为支持导入自定义数据，不再使用固定的测试数据。

## 数据格式要求

### 文件格式
- 支持 `.txt` 和 `.csv` 文件
- 每行一条记录
- 字段之间用 **TAB** 分隔（不是空格）

### 数据格式
```
姓名[TAB]姓氏[TAB]出生日期[TAB]SSN
```

### 示例
```
SAMANTHA	STONE	07/21/1998	*********
JOHN	FITE	02/24/1997	*********
MICHAEL	HACKETT	04/05/1995	*********
```

### 字段说明
1. **姓名** (First Name): 英文大写
2. **姓氏** (Last Name): 英文大写  
3. **出生日期**: MM/DD/YYYY 格式（如：07/21/1998）
4. **SSN**: 9位数字（如：*********）

## 使用步骤

### 1. 准备数据文件
- 创建一个文本文件（.txt 或 .csv）
- 按照上述格式填入数据
- 确保使用TAB分隔符，不是空格

### 2. 导入数据
1. 打开FSA网站：https://studentaid.gov/fsa-id/create-account/
2. 脚本面板会自动出现在右上角
3. 点击"选择文件"按钮，选择您的数据文件
4. 点击"导入数据"按钮
5. 系统会显示成功导入的数据条数

### 3. 开始测试
1. 确认数据导入成功后，"开始测试"按钮会变为可用
2. 点击"开始测试"开始自动测试
3. 脚本会自动填写表单并检测账户状态

### 4. 查看结果
- 测试过程中可以在面板中实时查看进度
- 测试完成后点击"导出结果"保存CSV报告

## 功能按钮说明

- **导入数据**: 从文件导入测试数据
- **清空数据**: 清除所有导入的数据和测试结果
- **开始测试**: 开始自动测试（需要先导入数据）
- **停止测试**: 停止当前测试
- **导出结果**: 将测试结果导出为CSV文件

## 测试结果说明

- **✓ 可注册**: 该账户信息可以用于注册
- **✗ 已存在**: 该账户信息已被注册
- **? 未知**: 测试过程中出现错误或未知状态

## 注意事项

1. **数据格式**: 必须严格按照TAB分隔的格式，日期格式为MM/DD/YYYY
2. **数据保存**: 导入的数据会自动保存，下次打开页面时会自动加载
3. **测试中断**: 如果测试被中断，可以重新开始，已测试的结果会保留
4. **网络问题**: 如果网络不稳定，脚本会自动重试（最多3次）

## 示例数据文件

项目中包含了 `sample_data.txt` 文件作为格式参考。
