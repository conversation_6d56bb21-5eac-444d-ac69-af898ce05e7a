/**
 * FSA账户测试 - MCP执行版本
 * 专门用于在MCP环境中直接执行的简化版本
 */

// 测试数据
const testData = [
    { firstName: "JOHN", lastName: "FITE", birthDate: { month: "02", day: "24", year: "1997" }, ssn: "223797268" },
    { firstName: "MICHAEL", lastName: "HACKETT", birthDate: { month: "04", day: "05", year: "1995" }, ssn: "435915241" },
    { firstName: "SHOMARI", lastName: "PASLEY", birthDate: { month: "09", day: "20", year: "1992" }, ssn: "231659084" },
    { firstName: "CHRISTOPHER", lastName: "VOSS", birthDate: { month: "07", day: "03", year: "1991" }, ssn: "223631709" },
    { firstName: "ANTHONY", lastName: "MONTOYA", birthDate: { month: "10", day: "30", year: "1992" }, ssn: "139941837" },
    { firstName: "SAMANTHA", lastName: "STONE", birthDate: { month: "07", day: "21", year: "1998" }, ssn: "231811389" },
    { firstName: "NATALIE", lastName: "VALDES", birthDate: { month: "05", day: "05", year: "1992" }, ssn: "593239270" },
    { firstName: "MICHELLE", lastName: "LOPEZ", birthDate: { month: "03", day: "25", year: "1991" }, ssn: "*********" }
];

// 测试结果存储
const testResults = [];
let currentIndex = 0;

/**
 * 在MCP环境中执行的主函数
 * 复制此函数内容到MCP控制台执行
 */
async function runMcpTest() {
    console.log("=== FSA账户测试开始 ===");
    
    try {
        // 导航到FSA网站
        console.log("导航到FSA账户创建页面...");
        await mcp_playwright_browser_navigate({
            url: "https://studentaid.gov/fsa-id/create-account/launch"
        });
        
        // 等待页面加载
        await mcp_playwright_browser_wait_for({
            time: 5
        });
        
        // 获取页面快照
        const snapshot = await mcp_playwright_browser_snapshot({
            random_string: "get_snapshot"
        });
        
        // 检查是否需要点击Get Started按钮
        if (!snapshot.Page.URL.includes('personal-info')) {
            console.log("点击Get Started按钮...");
            
            // 查找Get Started按钮
            const getStartedButton = findButtonByText(snapshot, "Get Started");
            if (getStartedButton) {
                await mcp_playwright_browser_click({
                    element: "Get Started按钮",
                    ref: getStartedButton
                });
                
                // 等待导航完成
                await mcp_playwright_browser_wait_for({
                    time: 5
                });
                
                console.log("已进入个人信息页面");
            } else {
                console.error("未找到Get Started按钮");
                return;
            }
        } else {
            console.log("已经在个人信息页面");
        }
        
        // 开始测试循环
        await processNextTest();
        
    } catch (error) {
        console.error("测试执行过程中发生错误:", error);
    }
}

/**
 * 处理下一个测试
 */
async function processNextTest() {
    if (currentIndex >= testData.length) {
        console.log("所有测试完成!");
        showFinalResults();
        return;
    }

    const testItem = testData[currentIndex];
    console.log(`测试第 ${currentIndex + 1}/${testData.length} 条数据: ${testItem.firstName} ${testItem.lastName}`);
    
    try {
        // 获取页面快照
        const snapshot = await mcp_playwright_browser_snapshot({
            random_string: "get_snapshot"
        });
        
        // 填写表单
        await fillForm(testItem, snapshot);
        
        // 提交表单
        await submitForm(snapshot);
        
        // 等待页面响应
        await mcp_playwright_browser_wait_for({
            time: 3
        });
        
        // 检查结果
        await checkResult(testItem);
    } catch (error) {
        console.error(`测试项 ${currentIndex + 1} 执行失败:`, error);
        recordResult(testItem, "ERROR", `执行错误: ${error.message}`);
        
        // 继续下一个测试
        currentIndex++;
        await processNextTest();
    }
}

/**
 * 填写表单
 */
async function fillForm(testItem, snapshot) {
    console.log("填写表单...");
    
    try {
        // 查找表单字段
        const firstNameField = findInputByLabel(snapshot, "First Name");
        const lastNameField = findInputByLabel(snapshot, "Last Name");
        const monthField = findInputByLabel(snapshot, "Month");
        const dayField = findInputByLabel(snapshot, "Day");
        const yearField = findInputByLabel(snapshot, "Year");
        const ssnField = findInputByLabel(snapshot, "Social Security");
        
        // 填写姓名
        if (firstNameField) {
            await mcp_playwright_browser_type({
                element: "First Name文本框",
                ref: firstNameField,
                text: testItem.firstName
            });
        } else {
            throw new Error("未找到First Name输入框");
        }
        
        if (lastNameField) {
            await mcp_playwright_browser_type({
                element: "Last Name文本框",
                ref: lastNameField,
                text: testItem.lastName
            });
        } else {
            throw new Error("未找到Last Name输入框");
        }
        
        // 填写出生日期
        if (monthField) {
            await mcp_playwright_browser_type({
                element: "Month输入框",
                ref: monthField,
                text: testItem.birthDate.month
            });
        } else {
            throw new Error("未找到Month输入框");
        }
        
        if (dayField) {
            await mcp_playwright_browser_type({
                element: "Day输入框",
                ref: dayField,
                text: testItem.birthDate.day
            });
        } else {
            throw new Error("未找到Day输入框");
        }
        
        if (yearField) {
            await mcp_playwright_browser_type({
                element: "Year输入框",
                ref: yearField,
                text: testItem.birthDate.year
            });
        } else {
            throw new Error("未找到Year输入框");
        }
        
        // 填写SSN
        if (ssnField) {
            await mcp_playwright_browser_type({
                element: "Social Security Number文本框",
                ref: ssnField,
                text: testItem.ssn
            });
        } else {
            throw new Error("未找到SSN输入框");
        }
        
        console.log("表单填写完成");
    } catch (error) {
        console.error("填写表单时出错:", error);
        throw error;
    }
}

/**
 * 提交表单
 */
async function submitForm(snapshot) {
    console.log("提交表单...");
    
    try {
        // 查找Continue按钮
        const continueButton = findButtonByText(snapshot, "Continue");
        
        if (!continueButton) {
            throw new Error("未找到Continue按钮");
        }
        
        // 点击Continue按钮
        await mcp_playwright_browser_click({
            element: "Continue按钮",
            ref: continueButton
        });
        
        console.log("表单已提交");
    } catch (error) {
        console.error("提交表单时出错:", error);
        throw error;
    }
}

/**
 * 检查结果
 */
async function checkResult(testItem) {
    console.log("检查结果...");
    
    try {
        // 获取页面快照
        const snapshot = await mcp_playwright_browser_snapshot({
            random_string: "get_snapshot"
        });
        
        // 检查URL和页面内容
        const pageUrl = snapshot.Page.URL;
        
        // 检查是否有错误信息
        const hasError = findAlertWithText(snapshot, "Account already exists");
        
        if (hasError) {
            console.log(`账户已存在: ${testItem.firstName} ${testItem.lastName}`);
            recordResult(testItem, "EXISTS", "账户已存在");
        } else if (pageUrl.includes('account-info')) {
            console.log(`账户创建成功: ${testItem.firstName} ${testItem.lastName}`);
            recordResult(testItem, "SUCCESS", "可以创建账户");
            
            // 返回上一步
            const previousButton = findButtonByText(snapshot, "Previous");
            if (previousButton) {
                await mcp_playwright_browser_click({
                    element: "Previous按钮",
                    ref: previousButton
                });
                
                await mcp_playwright_browser_wait_for({
                    time: 3
                });
            }
        } else {
            // 再等待一段时间检查
            await mcp_playwright_browser_wait_for({
                time: 2
            });
            
            // 再次检查
            await checkResultAgain(testItem);
            return;
        }
        
        // 继续下一个测试
        currentIndex++;
        await processNextTest();
    } catch (error) {
        console.error("检查结果时出错:", error);
        recordResult(testItem, "ERROR", `检查错误: ${error.message}`);
        
        currentIndex++;
        await processNextTest();
    }
}

/**
 * 再次检查结果
 */
async function checkResultAgain(testItem) {
    try {
        // 获取页面快照
        const snapshot = await mcp_playwright_browser_snapshot({
            random_string: "get_snapshot"
        });
        
        // 检查URL和页面内容
        const pageUrl = snapshot.Page.URL;
        
        if (pageUrl.includes('account-info')) {
            console.log(`账户创建成功: ${testItem.firstName} ${testItem.lastName}`);
            recordResult(testItem, "SUCCESS", "可以创建账户");
            
            // 返回上一步
            const previousButton = findButtonByText(snapshot, "Previous");
            if (previousButton) {
                await mcp_playwright_browser_click({
                    element: "Previous按钮",
                    ref: previousButton
                });
                
                await mcp_playwright_browser_wait_for({
                    time: 3
                });
            }
        } else {
            const hasError = findAlertWithText(snapshot, "Account already exists");
            if (hasError) {
                console.log(`账户已存在: ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "EXISTS", "账户已存在");
            } else {
                console.log(`未知状态: ${testItem.firstName} ${testItem.lastName}`);
                recordResult(testItem, "UNKNOWN", "未知状态");
            }
        }
        
        // 继续下一个测试
        currentIndex++;
        await processNextTest();
    } catch (error) {
        console.error("再次检查结果时出错:", error);
        recordResult(testItem, "ERROR", `检查错误: ${error.message}`);
        
        currentIndex++;
        await processNextTest();
    }
}

/**
 * 记录测试结果
 */
function recordResult(testItem, status, message) {
    const result = {
        index: currentIndex + 1,
        firstName: testItem.firstName,
        lastName: testItem.lastName,
        birthDate: `${testItem.birthDate.month}/${testItem.birthDate.day}/${testItem.birthDate.year}`,
        ssn: testItem.ssn,
        status: status,
        message: message,
        timestamp: new Date().toLocaleString()
    };
    
    testResults.push(result);
    console.log("结果已记录:", result);
}

/**
 * 显示最终测试结果
 */
function showFinalResults() {
    console.log("\n===== 测试结果汇总 =====");
    
    const successCount = testResults.filter(r => r.status === "SUCCESS").length;
    const existsCount = testResults.filter(r => r.status === "EXISTS").length;
    const errorCount = testResults.filter(r => r.status === "ERROR" || r.status === "UNKNOWN").length;
    
    console.log(`总测试数量: ${testResults.length}`);
    console.log(`可注册账户: ${successCount}`);
    console.log(`账户已存在: ${existsCount}`);
    console.log(`错误/未知: ${errorCount}`);
    
    console.log("\n详细结果:");
    testResults.forEach((result, index) => {
        const statusSymbol = result.status === "SUCCESS" ? "✓" : 
                           result.status === "EXISTS" ? "✗" : "?";
        
        console.log(`${index + 1}. ${statusSymbol} ${result.firstName} ${result.lastName} (${result.ssn}): ${result.message}`);
    });
    
    console.log("\n===== CSV格式结果 =====");
    let csvContent = "Index,FirstName,LastName,BirthDate,SSN,Status,Message,Timestamp\n";
    testResults.forEach(result => {
        csvContent += `${result.index},"${result.firstName}","${result.lastName}","${result.birthDate}","${result.ssn}","${result.status}","${result.message}","${result.timestamp}"\n`;
    });
    
    console.log(csvContent);
    console.log("\n测试完成!");
}

/**
 * 查找带有特定文本的按钮
 */
function findButtonByText(snapshot, text) {
    try {
        // 遍历页面元素查找按钮
        const findButton = (node) => {
            if (!node) return null;
            
            // 检查当前节点是否是按钮
            if (node.role === 'button' && 
                node.text && node.text.includes(text)) {
                return node.ref;
            }
            
            // 检查子节点
            if (node.children) {
                for (const child of node.children) {
                    const ref = findButton(child);
                    if (ref) return ref;
                }
            }
            
            return null;
        };
        
        return findButton(snapshot);
    } catch (error) {
        console.error(`查找按钮"${text}"时出错:`, error);
        return null;
    }
}

/**
 * 查找带有特定标签的输入框
 */
function findInputByLabel(snapshot, label) {
    try {
        // 遍历页面元素查找输入框
        const findInput = (node) => {
            if (!node) return null;
            
            // 检查当前节点是否是输入框
            if ((node.role === 'textbox' || node.role === 'spinbutton') && 
                node.name && node.name.includes(label)) {
                return node.ref;
            }
            
            // 检查子节点
            if (node.children) {
                for (const child of node.children) {
                    const ref = findInput(child);
                    if (ref) return ref;
                }
            }
            
            return null;
        };
        
        return findInput(snapshot);
    } catch (error) {
        console.error(`查找输入框"${label}"时出错:`, error);
        return null;
    }
}

/**
 * 查找带有特定文本的警告框
 */
function findAlertWithText(snapshot, text) {
    try {
        // 遍历页面元素查找警告框
        const findAlert = (node) => {
            if (!node) return false;
            
            // 检查当前节点是否是警告框
            if (node.role === 'alert') {
                // 检查当前节点的文本
                if (node.text && node.text.includes(text)) {
                    return true;
                }
                
                // 检查子节点的文本
                if (node.children) {
                    for (const child of node.children) {
                        if (child.text && child.text.includes(text)) {
                            return true;
                        }
                    }
                }
            }
            
            // 检查子节点
            if (node.children) {
                for (const child of node.children) {
                    if (findAlert(child)) {
                        return true;
                    }
                }
            }
            
            return false;
        };
        
        return findAlert(snapshot);
    } catch (error) {
        console.error(`查找警告框"${text}"时出错:`, error);
        return false;
    }
}

// 在MCP环境中运行测试
// 请复制整个函数内容到MCP控制台执行
// runMcpTest(); 