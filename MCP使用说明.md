# FSA账户测试工具 - MCP使用说明

## 概述

本文档提供了如何在MCP环境中使用FSA账户测试工具的简明指南。由于MCP环境的特殊性，我们需要使用MCP内置的Playwright API来执行测试，而不是传统的油猴脚本。

## 文件说明

1. `mcp-execute.js` - 专为MCP环境设计的测试脚本
2. `MCP使用说明.md` - 本使用说明文档

## 在MCP环境中执行测试

### 方法1: 直接复制代码到MCP控制台

1. 打开MCP浏览器，导航到 `https://studentaid.gov/fsa-id/create-account/launch`
2. 打开MCP控制台
3. 复制 `mcp-execute.js` 文件中的 `runMcpTest()` 函数及其相关函数到控制台
4. 执行 `runMcpTest()` 函数

### 方法2: 使用MCP API执行测试

以下是使用MCP API执行测试的步骤：

1. **导航到FSA网站**
   ```javascript
   await mcp_playwright_browser_navigate({
       url: "https://studentaid.gov/fsa-id/create-account/launch"
   });
   ```

2. **等待页面加载**
   ```javascript
   await mcp_playwright_browser_wait_for({
       time: 5
   });
   ```

3. **获取页面快照**
   ```javascript
   const snapshot = await mcp_playwright_browser_snapshot({
       random_string: "get_snapshot"
   });
   ```

4. **点击"Get Started"按钮**（如果需要）
   ```javascript
   // 查找Get Started按钮
   const getStartedButton = findButtonByText(snapshot, "Get Started");
   if (getStartedButton) {
       await mcp_playwright_browser_click({
           element: "Get Started按钮",
           ref: getStartedButton
       });
   }
   ```

5. **填写表单**
   ```javascript
   // 查找First Name输入框
   const firstNameField = findInputByLabel(snapshot, "First Name");
   if (firstNameField) {
       await mcp_playwright_browser_type({
           element: "First Name文本框",
           ref: firstNameField,
           text: "JOHN"
       });
   }
   
   // 类似地填写其他字段...
   ```

6. **提交表单**
   ```javascript
   // 查找Continue按钮
   const continueButton = findButtonByText(snapshot, "Continue");
   if (continueButton) {
       await mcp_playwright_browser_click({
           element: "Continue按钮",
           ref: continueButton
       });
   }
   ```

7. **检查结果**
   ```javascript
   // 获取新的页面快照
   const resultSnapshot = await mcp_playwright_browser_snapshot({
       random_string: "get_result"
   });
   
   // 检查是否有错误信息
   const hasError = findAlertWithText(resultSnapshot, "Account already exists");
   
   if (hasError) {
       console.log("账户已存在");
   } else if (resultSnapshot.Page.URL.includes('account-info')) {
       console.log("账户创建成功");
   }
   ```

## 辅助函数

以下是一些辅助函数，用于在MCP环境中查找页面元素：

### 查找按钮
```javascript
function findButtonByText(snapshot, text) {
    const findButton = (node) => {
        if (!node) return null;
        
        if (node.role === 'button' && 
            node.text && node.text.includes(text)) {
            return node.ref;
        }
        
        if (node.children) {
            for (const child of node.children) {
                const ref = findButton(child);
                if (ref) return ref;
            }
        }
        
        return null;
    };
    
    return findButton(snapshot);
}
```

### 查找输入框
```javascript
function findInputByLabel(snapshot, label) {
    const findInput = (node) => {
        if (!node) return null;
        
        if ((node.role === 'textbox' || node.role === 'spinbutton') && 
            node.name && node.name.includes(label)) {
            return node.ref;
        }
        
        if (node.children) {
            for (const child of node.children) {
                const ref = findInput(child);
                if (ref) return ref;
            }
        }
        
        return null;
    };
    
    return findInput(snapshot);
}
```

### 查找警告框
```javascript
function findAlertWithText(snapshot, text) {
    const findAlert = (node) => {
        if (!node) return false;
        
        if (node.role === 'alert') {
            if (node.text && node.text.includes(text)) {
                return true;
            }
            
            if (node.children) {
                for (const child of node.children) {
                    if (child.text && child.text.includes(text)) {
                        return true;
                    }
                }
            }
        }
        
        if (node.children) {
            for (const child of node.children) {
                if (findAlert(child)) {
                    return true;
                }
            }
        }
        
        return false;
    };
    
    return findAlert(snapshot);
}
```

## 注意事项

1. **MCP环境限制**
   - MCP环境有内容安全策略(CSP)和权限策略限制
   - 页面可能有资源加载错误和警告
   - 表单元素的选择器可能需要根据实际页面结构调整

2. **测试频率**
   - 请避免频繁运行测试，以免对服务器造成压力
   - 建议测试间隔不少于5分钟

3. **错误处理**
   - 脚本包含基本的错误处理机制
   - 如遇到未捕获的错误，请检查控制台日志

4. **数据安全**
   - 测试数据仅在MCP环境中使用
   - 不会向第三方发送任何数据

## 测试结果说明

测试结果包含以下状态：

- **SUCCESS**: ✓ 可以创建账户
- **EXISTS**: ✗ 账户已存在  
- **ERROR**: ❓ 测试过程中出现错误
- **UNKNOWN**: ❓ 未知状态

---

**免责声明**: 本工具仅供学习和研究目的使用。使用者应当遵守相关法律法规和网站的使用条款，并承担使用风险和责任。 